"use client"

import { useState, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"
import {
  Search,
  Plus,
  Music2,
  Star,
  Filter,
  X,
  Play,
  Pause
} from "lucide-react"
import { useGetMusics } from "@schemas/Music/music-query"
import { useAddMusicToMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { useAudioStore } from "@/lib/audio-store"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { AddToPlaylistDialog } from "../../_components/add-to-playlist-dialog"

interface Music {
  id: string
  title: string
  src: string | null
  genres: string[]
  isPublic: boolean
  rating?: number | null
}

export function MusicsRoute() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedGenres, setSelectedGenres] = useState<string[]>([])
  const [selectedMusicId, setSelectedMusicId] = useState<string | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  const { data: musics, isLoading } = useGetMusics({ isPublic: true })
  const addMusicToPlaylist = useAddMusicToMusicPlaylistUser()

  // Global audio player state
  const {
    globalPlayer,
    setGlobalPlayerTrack,
    setGlobalPlayerPlaying
  } = useAudioStore()

  // Get unique genres for filtering
  const availableGenres = useMemo(() => {
    if (!musics) return []
    const genreSet = new Set<string>()
    musics.forEach(music => {
      music.genres?.forEach(genre => genreSet.add(genre))
    })
    return Array.from(genreSet).sort()
  }, [musics])

  // Filter musics based on search and genre filters
  const filteredMusics = useMemo(() => {
    if (!musics) return []
    
    return musics.filter(music => {
      const matchesSearch = music.title.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesGenre = selectedGenres.length === 0 || 
        music.genres?.some(genre => selectedGenres.includes(genre))
      
      return matchesSearch && matchesGenre
    })
  }, [musics, searchQuery, selectedGenres])

  const handleGenreToggle = (genre: string) => {
    setSelectedGenres(prev => 
      prev.includes(genre) 
        ? prev.filter(g => g !== genre)
        : [...prev, genre]
    )
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedGenres([])
  }

  const handleAddToPlaylist = (musicId: string) => {
    setSelectedMusicId(musicId)
    setIsAddDialogOpen(true)
  }

  const handleAddMusicToPlaylist = async (playlistId: string) => {
    if (!selectedMusicId) return

    await addMusicToPlaylist.mutateAsync({
      musicPlaylistUserId: playlistId,
      musicIds: [selectedMusicId]
    })
  }

  const handlePlayMusic = (music: Music) => {
    const track = {
      id: music.id,
      title: music.title,
      src: music.src || `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`,
      type: 'music' as const,
      genres: music.genres
    }

    if (globalPlayer.currentTrack?.id === music.id) {
      // If already playing this track, toggle play/pause
      setGlobalPlayerPlaying(!globalPlayer.isPlaying)
    } else {
      // Play new track - set track first, then ensure it starts playing
      setGlobalPlayerTrack(track)
      setGlobalPlayerPlaying(true)
    }
  }

  const selectedMusic = selectedMusicId ? musics?.find(m => m.id === selectedMusicId) : null

  return (
    <>
      <div className="space-y-6 pb-32">
        {/* Header Section */}
        <div className="space-y-4">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Musics
            </h1>
            <p className="text-muted-foreground mt-1">
              Discover and add music tracks to enhance your focus sessions
            </p>
          </div>

          {/* Search and Filters Card */}
          <Card>
            <CardContent className="p-4 space-y-4">
              <div className="flex flex-col sm:flex-row gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search music tracks..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {(searchQuery || selectedGenres.length > 0) && (
                  <Button
                    variant="outline"
                    onClick={clearFilters}
                    className="gap-2 shrink-0"
                  >
                    <X className="h-4 w-4" />
                    Clear Filters
                  </Button>
                )}
              </div>

              {/* Genre Filters */}
              {availableGenres.length > 0 && (
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Filter className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm font-medium">Genres</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {availableGenres.map(genre => (
                      <Badge
                        key={genre}
                        variant={selectedGenres.includes(genre) ? "default" : "outline"}
                        className={cn(
                          "cursor-pointer transition-all duration-200 hover:scale-105",
                          selectedGenres.includes(genre)
                            ? "bg-primary text-primary-foreground hover:bg-primary/90"
                            : "hover:bg-accent hover:text-accent-foreground"
                        )}
                        onClick={() => handleGenreToggle(genre)}
                      >
                        {genre}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Music Grid */}
        <div>
          {isLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {Array.from({ length: 6 }).map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.2, delay: i * 0.03 }}
                >
                  <Card>
                    <CardContent className="p-3">
                      <div className="flex items-center gap-3">
                        <Skeleton className="h-10 w-10 rounded-lg shrink-0" />
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-3/4" />
                          <div className="flex gap-1">
                            <Skeleton className="h-3 w-12" />
                            <Skeleton className="h-3 w-16" />
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          ) : filteredMusics.length === 0 ? (
            <div className="text-center py-12">
              <div className="p-4 rounded-full bg-muted/20 w-fit mx-auto mb-4">
                <Music2 className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="font-semibold text-lg mb-2">No music tracks found</h3>
              <p className="text-muted-foreground">
                {searchQuery || selectedGenres.length > 0
                  ? "Try adjusting your search or filters"
                  : "No music tracks are available at the moment"
                }
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              <AnimatePresence mode="popLayout">
                {filteredMusics.map((music, index) => {
                  const isCurrentlyPlaying = globalPlayer.currentTrack?.id === music.id && globalPlayer.isPlaying
                  const isCurrentTrack = globalPlayer.currentTrack?.id === music.id

                  return (
                    <motion.div
                      key={music.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{
                        duration: 0.2,
                        delay: index * 0.02,
                        ease: "easeOut"
                      }}
                      className="will-change-transform"
                    >
                      <Card
                        className={cn(
                          "group relative overflow-hidden transition-all duration-200 cursor-pointer h-full",
                          "hover:shadow-lg hover:-translate-y-1",
                          isCurrentTrack && "bg-accent/50 border-primary/20 ring-1 ring-primary/20"
                        )}
                      >
                        <CardContent className="p-3">
                          <div className="flex items-center gap-3 relative">
                            {/* Music Icon / Play Button */}
                            <div className="h-10 w-10 rounded-lg bg-muted/20 flex items-center justify-center shrink-0 relative">
                              <Button
                                variant="ghost"
                                size="icon"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handlePlayMusic(music)
                                }}
                                className="w-10 h-10 rounded-lg hover:bg-accent absolute inset-0 transition-colors duration-150"
                              >
                                {isCurrentlyPlaying ? (
                                  <Pause className="h-4 w-4 text-foreground" />
                                ) : (
                                  <Play className="h-4 w-4 text-foreground ml-0.5" />
                                )}
                              </Button>
                            </div>

                            {/* Music Info */}
                            <div className="flex-1 min-w-0">
                              <h3 className="font-medium line-clamp-1 mb-1.5 text-sm leading-tight">{music.title}</h3>
                              <div className="flex items-center gap-1.5 flex-wrap">
                                {music.genres?.slice(0, 2).map(genre => (
                                  <Badge
                                    key={genre}
                                    variant="secondary"
                                    className="text-xs px-1.5 py-0.5"
                                  >
                                    {genre}
                                  </Badge>
                                ))}
                                {music.rating && (
                                  <div className="flex items-center gap-1">
                                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                    <span className="text-xs text-muted-foreground">
                                      {music.rating.toFixed(1)}
                                    </span>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Add Button - Hover Only */}
                            <div className="absolute right-3 top-1/2 -translate-y-1/2 opacity-0 group-hover:opacity-100 transition-all duration-200 transform translate-x-2 group-hover:translate-x-0">
                              <Button
                                onClick={(e) => {
                                  e.stopPropagation()
                                  handleAddToPlaylist(music.id)
                                }}
                                size="sm"
                                className={cn(
                                  "h-7 px-3 text-xs transition-all duration-200 gap-1.5 shadow-lg",
                                  "bg-gradient-to-r from-red-500 to-rose-500 hover:from-red-600 hover:to-rose-600 text-white",
                                  "hover:shadow-xl hover:scale-105"
                                )}
                              >
                                <Plus className="h-3 w-3" />
                                Add
                              </Button>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )
                })}
              </AnimatePresence>
            </div>
          )}
        </div>

      {/* Add to Playlist Dialog */}
      <AddToPlaylistDialog
        isOpen={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        itemType="music"
        itemTitle={selectedMusic?.title || ""}
        onAddToPlaylist={handleAddMusicToPlaylist}
      />
      </div>
    </>
  )
}

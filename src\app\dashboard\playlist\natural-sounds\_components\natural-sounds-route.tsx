"use client"

import { useState, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import {
  Search,
  Plus,
  Filter,
  X,
  Play,
  Pause,
  Waves
} from "lucide-react"
import { useGetNatureSounds } from "@schemas/Natural/nature-sound-query"
import { useAddNatureSoundsToMusicPlaylistUser } from "@schemas/MusicPlaylist/music-playlist-user-query"
import { useAudioStore } from "@/lib/audio-store"
import { motion, AnimatePresence } from "framer-motion"
import { cn } from "@/lib/utils"
import { AddToNaturePlaylistDialog } from "./add-to-nature-playlist-dialog"

interface NatureSound {
  id: string
  title: string
  src: string | null
  category: string[]
  isPublic: boolean
}

// Map category enum values to display names
const categoryDisplayNames: Record<string, string> = {
  RAIN: "Rain",
  FOREST: "Forest",
  OCEAN: "Ocean",
  THUNDER: "Thunder",
  WIND: "Wind",
  FIRE: "Fire",
  BIRDS: "Birds",
  WATERFALL: "Waterfall",
  STREAM: "Stream",
  WAVES: "Waves",
  NIGHT: "Night",
  INSECTS: "Insects",
  RIVER: "River",
  STORM: "Storm",
  LAKE: "Lake",
  WHITE_NOISE: "White Noise",
  AMBIENT: "Ambient",
  CITY: "City",
  PEOPLE: "People"
}

export function NaturalSoundsRoute() {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategories, setSelectedCategories] = useState<string[]>([])
  const [selectedSoundId, setSelectedSoundId] = useState<string | null>(null)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)

  const { data: natureSounds, isLoading } = useGetNatureSounds({ isPublic: true })
  const addNatureSoundsToPlaylist = useAddNatureSoundsToMusicPlaylistUser()

  // Global audio player state
  const {
    globalPlayer,
    setGlobalPlayerTrack,
    setGlobalPlayerPlaying
  } = useAudioStore()

  // Get unique categories for filtering
  const availableCategories = useMemo(() => {
    if (!natureSounds) return []
    const categorySet = new Set<string>()
    natureSounds.forEach(sound => {
      sound.category?.forEach(cat => categorySet.add(cat))
    })
    return Array.from(categorySet).sort()
  }, [natureSounds])

  // Filter nature sounds based on search and category filters
  const filteredSounds = useMemo(() => {
    if (!natureSounds) return []
    
    return natureSounds.filter(sound => {
      const matchesSearch = sound.title.toLowerCase().includes(searchQuery.toLowerCase())
      const matchesCategory = selectedCategories.length === 0 || 
        sound.category?.some(cat => selectedCategories.includes(cat))
      
      return matchesSearch && matchesCategory
    })
  }, [natureSounds, searchQuery, selectedCategories])

  const handleCategoryToggle = (category: string) => {
    setSelectedCategories(prev => 
      prev.includes(category) 
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }

  const clearFilters = () => {
    setSearchQuery("")
    setSelectedCategories([])
  }

  const handleAddToPlaylist = (soundId: string) => {
    setSelectedSoundId(soundId)
    setIsAddDialogOpen(true)
  }

  const handleAddSoundToPlaylist = async (playlistId: string) => {
    if (!selectedSoundId) return

    await addNatureSoundsToPlaylist.mutateAsync({
      musicPlaylistUserId: playlistId,
      natureSoundIds: [selectedSoundId]
    })
  }

  const handlePlaySound = (sound: NatureSound) => {
    const track = {
      id: sound.id,
      title: sound.title,
      src: sound.src || `https://www.soundjay.com/misc/sounds/bell-ringing-05.wav`,
      type: 'nature-sound' as const,
      category: sound.category
    }

    if (globalPlayer.currentTrack?.id === sound.id) {
      // If already playing this track, toggle play/pause
      setGlobalPlayerPlaying(!globalPlayer.isPlaying)
    } else {
      // Play new track - set track first, then ensure it starts playing
      setGlobalPlayerTrack(track)
      setGlobalPlayerPlaying(true)
    }
  }

  const selectedSound = selectedSoundId ? natureSounds?.find(s => s.id === selectedSoundId) : null

  return (
    <>
      <div className="space-y-6 pb-32">
      {/* Header */}
      <div className="space-y-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Natural Sounds</h1>
          <p className="text-muted-foreground">
            Discover and add natural sounds to create the perfect ambient environment
          </p>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search natural sounds..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          {(searchQuery || selectedCategories.length > 0) && (
            <Button
              variant="outline"
              onClick={clearFilters}
              className="gap-2 shrink-0"
            >
              <X className="h-4 w-4" />
              Clear Filters
            </Button>
          )}
        </div>

        {/* Category Filters */}
        {availableCategories.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Categories</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {availableCategories.map(category => (
                <Badge
                  key={category}
                  variant={selectedCategories.includes(category) ? "default" : "outline"}
                  className={cn(
                    "cursor-pointer transition-all duration-200 hover:scale-105",
                    selectedCategories.includes(category) && "bg-primary text-primary-foreground"
                  )}
                  onClick={() => handleCategoryToggle(category)}
                >
                  {categoryDisplayNames[category] || category}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Natural Sounds List */}
      <div className="space-y-4">
        {isLoading ? (
          <div className="grid gap-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: i * 0.05 }}
                className="flex items-center gap-4 p-4 rounded-lg border border-border/50 bg-muted/10"
              >
                <Skeleton className="h-12 w-12 rounded-lg" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <div className="flex gap-2">
                    <Skeleton className="h-3 w-16" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                </div>
                <Skeleton className="h-8 w-20" />
              </motion.div>
            ))}
          </div>
        ) : filteredSounds.length === 0 ? (
          <div className="text-center py-12">
            <div className="p-4 rounded-full bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 w-fit mx-auto mb-4">
              <Waves className="h-8 w-8 text-orange-600 dark:text-orange-400" />
            </div>
            <h3 className="font-semibold text-lg mb-2">No natural sounds found</h3>
            <p className="text-muted-foreground">
              {searchQuery || selectedCategories.length > 0
                ? "Try adjusting your search or filters"
                : "No natural sounds are available at the moment"
              }
            </p>
          </div>
        ) : (
          <div className="grid gap-3">
            <AnimatePresence>
              {filteredSounds.map((sound, index) => {
                const isCurrentlyPlaying = globalPlayer.currentTrack?.id === sound.id && globalPlayer.isPlaying
                const isCurrentTrack = globalPlayer.currentTrack?.id === sound.id

                return (
                  <motion.div
                    key={sound.id}
                    layout
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -5 }}
                    transition={{ duration: 0.2, delay: index * 0.02 }}
                    className={cn(
                      "group flex items-center gap-4 p-4 rounded-lg border transition-all duration-200",
                      "border-border/30 hover:border-border hover:bg-muted/20",
                      isCurrentTrack && "bg-orange-50/50 dark:bg-orange-950/20 border-orange-200 dark:border-orange-800"
                    )}
                  >
                    {/* Sound Icon / Play Button */}
                    <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-orange-100 to-rose-100 dark:from-orange-900/30 dark:to-rose-900/30 flex items-center justify-center shrink-0 relative">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation()
                          handlePlaySound(sound)
                        }}
                        className="w-12 h-12 rounded-lg hover:bg-orange-200 dark:hover:bg-orange-800 absolute inset-0"
                      >
                        {isCurrentlyPlaying ? (
                          <Pause className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                        ) : (
                          <Play className="h-5 w-5 text-orange-600 dark:text-orange-400 ml-0.5" />
                        )}
                      </Button>
                    </div>

                  {/* Sound Info */}
                  <div className="flex-1 min-w-0">
                    <h3 className="font-medium line-clamp-1 mb-1">{sound.title}</h3>
                    <div className="flex items-center gap-2 flex-wrap">
                      {sound.category?.slice(0, 3).map(category => (
                        <Badge key={category} variant="secondary" className="text-xs">
                          {categoryDisplayNames[category] || category}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  {/* Add Button */}
                  <Button
                    onClick={(e) => {
                      e.stopPropagation()
                      handleAddToPlaylist(sound.id)
                    }}
                    size="sm"
                    className={cn(
                      "h-8 px-3 text-xs transition-all duration-200 gap-2",
                      "bg-gradient-to-r from-orange-500 to-rose-500 hover:from-orange-600 hover:to-rose-600 text-white"
                    )}
                  >
                    <Plus className="h-3 w-3" />
                    Add to Playlist
                  </Button>
                  </motion.div>
                )
              })}
            </AnimatePresence>
          </div>
        )}
      </div>

      {/* Add to Nature Playlist Dialog */}
      <AddToNaturePlaylistDialog
        isOpen={isAddDialogOpen}
        onOpenChange={setIsAddDialogOpen}
        soundId={selectedSoundId || ""}
        soundTitle={selectedSound?.title || ""}
        onAddToPlaylist={handleAddSoundToPlaylist}
      />
      </div>
    </>
  )
}
